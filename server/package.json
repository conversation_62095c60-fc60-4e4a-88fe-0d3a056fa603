{"name": "ecommerce-backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.7.9", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.5", "multer": "^2.0.0", "node-cron": "^4.1.0", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "server": "file:", "serverless-http": "^3.2.0", "sib-api-v3-sdk": "^8.5.0", "slugify": "^1.6.6", "stripe": "^17.6.0", "uuid": "^11.1.0"}}