import User from "../models/User.js";
import Order from "../models/Order.js";
import Product from "../models/Product.js";
import Category from "../models/Category.js";

const getAdminDashboard = async (req, res) => {
    try {
        // Get total counts
        const totalUsers = await User.countDocuments();
        const totalOrders = await Order.countDocuments();
        const totalProducts = await Product.countDocuments();
        const totalCategories = await Category.countDocuments();

        // Calculate total revenue
        const revenueResult = await Order.aggregate([
            { $match: { status: { $ne: 'cancelled' } } },
            { $group: { _id: null, total: { $sum: "$totalAmount" } } }
        ]);
        const totalRevenue = revenueResult[0]?.total || 0;

        // Get recent orders
        const recentOrders = await Order.find()
            .populate('user', 'fullName email')
            .sort({ createdAt: -1 })
            .limit(10);

        // Get revenue analytics for the last 7 days
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const revenueAnalytics = await Order.aggregate([
            {
                $match: {
                    createdAt: { $gte: sevenDaysAgo },
                    status: { $ne: 'cancelled' }
                }
            },
            {
                $group: {
                    _id: {
                        $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
                    },
                    revenue: { $sum: "$totalAmount" },
                    orders: { $sum: 1 }
                }
            },
            { $sort: { _id: 1 } }
        ]);

        // Get top categories by revenue
        const topCategories = await Order.aggregate([
            { $match: { status: { $ne: 'cancelled' } } },
            { $unwind: "$items" },
            {
                $lookup: {
                    from: "products",
                    localField: "items.product",
                    foreignField: "_id",
                    as: "productInfo"
                }
            },
            { $unwind: "$productInfo" },
            {
                $lookup: {
                    from: "categories",
                    localField: "productInfo.category",
                    foreignField: "_id",
                    as: "categoryInfo"
                }
            },
            { $unwind: "$categoryInfo" },
            {
                $group: {
                    _id: "$categoryInfo.name",
                    value: { $sum: { $multiply: ["$items.quantity", "$items.price"] } }
                }
            },
            { $sort: { value: -1 } },
            { $limit: 5 }
        ]);

        // Get user growth stats
        const userGrowth = await User.aggregate([
            {
                $group: {
                    _id: {
                        $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
                    },
                    newUsers: { $sum: 1 }
                }
            },
            { $sort: { _id: 1 } },
            { $limit: 30 }
        ]);

        return res.status(200).json({
            stats: {
                totalUsers,
                totalOrders,
                totalRevenue,
                totalProducts,
                totalCategories
            },
            recentOrders,
            revenueAnalytics,
            topCategories,
            userGrowth
        });
    } catch (error) {
        console.error('Admin dashboard error:', error);
        return res.status(500).json({ message: "Server error" });
    }
}

// Get all users with pagination and filtering
const getAllUsers = async (req, res) => {
    try {
        const { page = 1, limit = 10, search = '', status = '' } = req.query;
        const skip = (page - 1) * limit;

        let query = {};
        if (search) {
            query.$or = [
                { fullName: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } }
            ];
        }
        if (status) {
            query.status = status;
        }

        const users = await User.find(query)
            .select('-password')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const total = await User.countDocuments(query);

        return res.status(200).json({
            users,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(total / limit),
                totalUsers: total,
                hasNext: page * limit < total,
                hasPrev: page > 1
            }
        });
    } catch (error) {
        console.error('Get all users error:', error);
        return res.status(500).json({ message: "Server error" });
    }
}

// Update user status (block/unblock)
const updateUserStatus = async (req, res) => {
    try {
        const { userId, status } = req.body;

        if (!['verified', 'blocked', 'unverified'].includes(status)) {
            return res.status(400).json({ message: "Invalid status" });
        }

        const user = await User.findByIdAndUpdate(
            userId,
            { status },
            { new: true }
        ).select('-password');

        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        return res.status(200).json({
            message: `User ${status} successfully`,
            user
        });
    } catch (error) {
        console.error('Update user status error:', error);
        return res.status(500).json({ message: "Server error" });
    }
}

// Get all orders with pagination and filtering
const getAllOrdersAdmin = async (req, res) => {
    try {
        const { page = 1, limit = 10, status = '', search = '' } = req.query;
        const skip = (page - 1) * limit;

        let query = {};
        if (status) {
            query.status = status;
        }
        if (search) {
            // Search by order ID or customer name
            const users = await User.find({
                fullName: { $regex: search, $options: 'i' }
            }).select('_id');

            query.$or = [
                { orderID: { $regex: search, $options: 'i' } },
                { user: { $in: users.map(u => u._id) } }
            ];
        }

        const orders = await Order.find(query)
            .populate('user', 'fullName email')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const total = await Order.countDocuments(query);

        return res.status(200).json({
            orders,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(total / limit),
                totalOrders: total,
                hasNext: page * limit < total,
                hasPrev: page > 1
            }
        });
    } catch (error) {
        console.error('Get all orders error:', error);
        return res.status(500).json({ message: "Server error" });
    }
}

// Update order status
const updateOrderStatus = async (req, res) => {
    try {
        const { orderId, status } = req.body;

        const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({ message: "Invalid status" });
        }

        const order = await Order.findByIdAndUpdate(
            orderId,
            { status },
            { new: true }
        ).populate('user', 'fullName email');

        if (!order) {
            return res.status(404).json({ message: "Order not found" });
        }

        return res.status(200).json({
            message: `Order status updated to ${status}`,
            order
        });
    } catch (error) {
        console.error('Update order status error:', error);
        return res.status(500).json({ message: "Server error" });
    }
}

// Get product analytics
const getProductAnalytics = async (req, res) => {
    try {
        // Top selling products
        const topSellingProducts = await Order.aggregate([
            { $match: { status: { $ne: 'cancelled' } } },
            { $unwind: "$items" },
            {
                $group: {
                    _id: "$items.product",
                    totalSold: { $sum: "$items.quantity" },
                    totalRevenue: { $sum: { $multiply: ["$items.quantity", "$items.price"] } }
                }
            },
            {
                $lookup: {
                    from: "products",
                    localField: "_id",
                    foreignField: "_id",
                    as: "productInfo"
                }
            },
            { $unwind: "$productInfo" },
            {
                $project: {
                    productName: "$productInfo.title",
                    totalSold: 1,
                    totalRevenue: 1
                }
            },
            { $sort: { totalSold: -1 } },
            { $limit: 10 }
        ]);

        // Low stock products
        const lowStockProducts = await Product.find({
            stock: { $lt: 10 }
        }).select('title stock').sort({ stock: 1 }).limit(10);

        // Products by category
        const productsByCategory = await Product.aggregate([
            {
                $lookup: {
                    from: "categories",
                    localField: "category",
                    foreignField: "_id",
                    as: "categoryInfo"
                }
            },
            { $unwind: "$categoryInfo" },
            {
                $group: {
                    _id: "$categoryInfo.name",
                    count: { $sum: 1 }
                }
            },
            { $sort: { count: -1 } }
        ]);

        return res.status(200).json({
            topSellingProducts,
            lowStockProducts,
            productsByCategory
        });
    } catch (error) {
        console.error('Get product analytics error:', error);
        return res.status(500).json({ message: "Server error" });
    }
}

// Get system statistics
const getSystemStats = async (req, res) => {
    try {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const startOfWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        // Today's stats
        const todayStats = await Order.aggregate([
            {
                $match: {
                    createdAt: { $gte: startOfDay },
                    status: { $ne: 'cancelled' }
                }
            },
            {
                $group: {
                    _id: null,
                    orders: { $sum: 1 },
                    revenue: { $sum: "$totalAmount" }
                }
            }
        ]);

        // This week's stats
        const weekStats = await Order.aggregate([
            {
                $match: {
                    createdAt: { $gte: startOfWeek },
                    status: { $ne: 'cancelled' }
                }
            },
            {
                $group: {
                    _id: null,
                    orders: { $sum: 1 },
                    revenue: { $sum: "$totalAmount" }
                }
            }
        ]);

        // This month's stats
        const monthStats = await Order.aggregate([
            {
                $match: {
                    createdAt: { $gte: startOfMonth },
                    status: { $ne: 'cancelled' }
                }
            },
            {
                $group: {
                    _id: null,
                    orders: { $sum: 1 },
                    revenue: { $sum: "$totalAmount" }
                }
            }
        ]);

        // New users today
        const newUsersToday = await User.countDocuments({
            createdAt: { $gte: startOfDay }
        });

        return res.status(200).json({
            today: {
                orders: todayStats[0]?.orders || 0,
                revenue: todayStats[0]?.revenue || 0,
                newUsers: newUsersToday
            },
            week: {
                orders: weekStats[0]?.orders || 0,
                revenue: weekStats[0]?.revenue || 0
            },
            month: {
                orders: monthStats[0]?.orders || 0,
                revenue: monthStats[0]?.revenue || 0
            }
        });
    } catch (error) {
        console.error('Get system stats error:', error);
        return res.status(500).json({ message: "Server error" });
    }
}

export {
    getAdminDashboard,
    getAllUsers,
    updateUserStatus,
    getAllOrdersAdmin,
    updateOrderStatus,
    getProductAnalytics,
    getSystemStats
}
