import FlashSale from "../models/FlashSaleSchema.js";
import Product from "../models/Product.js";
import slugify from "slugify";
import { enrichProductsWithFlashSale } from '../utils/enrichWithFlashSale.js';


const addProduct = async (req, res) => {
    try {
        const {
            storeName,
            title,
            category,
            price,
            description,
            stock,
            discount = 0,
            onSale = false,
            colors = [],
            sizes = [],
            mainImage,
            image1,
            image2,
            image3,
            image4
        } = req.body;

        // Validation
        if (!storeName || !title || !category || !price || !description || !stock || !mainImage || !image1 || !image2 || !image3 || !image4) {
            return res.status(400).json({ message: "All required fields must be provided" });
        }

        if (price <= 0 || stock < 0) {
            return res.status(400).json({ message: "Price must be positive and stock cannot be negative" });
        }

        const productCode = await generateProductCode();
        const slug = slugify(title, { lower: true, strict: true });

        const product = new Product({
            storeName,
            title,
            category,
            price: parseFloat(price),
            description,
            stock: parseInt(stock),
            discount: parseFloat(discount),
            onSale,
            colors: Array.isArray(colors) ? colors : [],
            sizes: Array.isArray(sizes) ? sizes : [],
            mainImage,
            image1,
            image2,
            image3,
            image4,
            productCode,
            slug
        });

        const savedProduct = await product.save();
        await savedProduct.populate('category', 'name');

        res.status(201).json({
            message: "Product created successfully",
            product: savedProduct
        });
    } catch (error) {
        console.log(error.message);
        if (error.name === 'ValidationError') {
            return res.status(400).json({ message: error.message });
        }
        res.status(500).json({ message: "Server error, please try again" });
    }
}

const generateProductCode = async () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    const code = Array.from({ length: 6 }, () =>
        chars[Math.floor(Math.random() * chars.length)]
    ).join("");
    const randomNum = Math.floor(100 + Math.random() * 900);
    const productCode = `${code}-${randomNum}`;

    const exists = await Product.findOne({ productCode });
    if (exists) return generateProductCode();
    return productCode;
};



const getBestSellingProducts = async (req, res) => {
    try {
        const products = await Product.find().sort({ salesVolume: -1, salesCount: -1 }).limit(10)

        const enriched = await enrichProductsWithFlashSale(products);
        res.json(enriched);
    } catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Server error, please try again" });
    }
};


const getProductByProductCode = async (req, res) => {
    const { productCode } = req.params;

    try {
        const product = await Product.findOne({ productCode }).populate("category flashSaleId")

        if (!product) {
            return res.status(404).json({ message: "Product not found" });
        }

        const enrichedProduct = await product.getWithFlashSale();

        res.json(enrichedProduct);
    } catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Server error, please try again" });
    }
};

const getProducts = async (req, res) => {
    try {
        const products = await Product.find({ onFlashSale: false })
        const enriched = await Promise.all(products.map(p => p.getWithFlashSale()));
        res.json(enriched);
    } catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Server error, please try again" });
    }
};



const updateProduct = async (req, res) => {
    const productID = req.params.id;
    try {
        const existingProduct = await Product.findById(productID);
        if (!existingProduct) {
            return res.status(404).json({ message: "Product not found" });
        }

        const {
            storeName,
            title,
            category,
            price,
            description,
            stock,
            discount,
            onSale,
            colors,
            sizes,
            mainImage,
            image1,
            image2,
            image3,
            image4
        } = req.body;

        // Validation
        if (price !== undefined && price <= 0) {
            return res.status(400).json({ message: "Price must be positive" });
        }
        if (stock !== undefined && stock < 0) {
            return res.status(400).json({ message: "Stock cannot be negative" });
        }

        // Update slug if title changed
        const updatedData = { ...req.body };
        if (title && title !== existingProduct.title) {
            updatedData.slug = slugify(title, { lower: true, strict: true });
        }

        const product = await Product.findByIdAndUpdate(
            productID,
            updatedData,
            { new: true, runValidators: true }
        ).populate('category', 'name');

        res.status(200).json({
            message: "Product updated successfully",
            product
        });
    } catch (error) {
        console.log(error.message);
        if (error.name === 'ValidationError') {
            return res.status(400).json({ message: error.message });
        }
        res.status(500).json({ message: "Server error, please try again" });
    }
}

const deleteProduct = async (req, res) => {
    const productID = req.params.productID;
    try {
        const product = await Product.findById(productID);
        if (!product) {
            return res.status(404).json({ message: "Product not found" });
        }

        await Product.findByIdAndDelete(productID);
        res.status(200).json({ message: "Product deleted successfully" });
    } catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Server error, please try again" });
    }
};

// Bulk operations
const bulkUpdateProducts = async (req, res) => {
    try {
        const { productIds, updateData } = req.body;

        if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
            return res.status(400).json({ message: "Product IDs array is required" });
        }

        const result = await Product.updateMany(
            { _id: { $in: productIds } },
            updateData,
            { runValidators: true }
        );

        res.status(200).json({
            message: `${result.modifiedCount} products updated successfully`,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Server error, please try again" });
    }
};

const bulkDeleteProducts = async (req, res) => {
    try {
        const { productIds } = req.body;

        if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
            return res.status(400).json({ message: "Product IDs array is required" });
        }

        const result = await Product.deleteMany({ _id: { $in: productIds } });

        res.status(200).json({
            message: `${result.deletedCount} products deleted successfully`,
            deletedCount: result.deletedCount
        });
    } catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Server error, please try again" });
    }
};

const getAllProducts = async (req, res) => {
    try {
        const { page = 1, limit = 20, search = '', category = '', status = '', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const skip = (page - 1) * limit;

        // Build query
        let query = {};

        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { productCode: { $regex: search, $options: 'i' } }
            ];
        }

        if (category) {
            query.category = category;
        }

        if (status === 'active') {
            query.stock = { $gt: 0 };
        } else if (status === 'inactive') {
            query.stock = { $lte: 0 };
        }

        // Build sort object
        const sortObj = {};
        sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

        const products = await Product.find(query)
            .populate('category', 'name')
            .sort(sortObj)
            .skip(skip)
            .limit(parseInt(limit));

        const total = await Product.countDocuments(query);
        const enriched = await Promise.all(products.map(p => p.getWithFlashSale()));

        res.json({
            products: enriched,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(total / limit),
                totalProducts: total,
                hasNext: page * limit < total,
                hasPrev: page > 1
            }
        });
    } catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Server error, please try again" });
    }
};


const searchProducts = async (req, res) => {
    try {
        const { q } = req.query;
        if (!q) return res.status(400).json({ message: 'No search query provided' });

        const products = await Product.find({ $or: [{ title: { $regex: q, $options: 'i' } }, { description: { $regex: q, $options: 'i' } }] }).populate('category')

        const enrichedProducts = await Promise.all(products.map(product => product.getWithFlashSale()));
        res.json({ products: enrichedProducts });
    } catch (err) {
        res.status(500).json({ message: 'Server error', error: err.message });
    }
};


// Get single product by ID for admin editing
const getProductById = async (req, res) => {
    try {
        const { id } = req.params;
        const product = await Product.findById(id).populate('category', 'name');

        if (!product) {
            return res.status(404).json({ message: "Product not found" });
        }

        res.status(200).json(product);
    } catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Server error, please try again" });
    }
};

export {
    addProduct,
    getBestSellingProducts,
    getProductByProductCode,
    getProducts,
    updateProduct,
    deleteProduct,
    getAllProducts,
    searchProducts,
    getProductById,
    bulkUpdateProducts,
    bulkDeleteProducts
};