import e from "express"
import {
    getAdminDashboard,
    getAllUsers,
    updateUserStatus,
    getAllOrdersAdmin,
    updateOrderStatus,
    getProductAnalytics,
    getSystemStats
} from "../controllers/adminController.js"

const router = e.Router()

// Dashboard routes
router.get("/dashboard", getAdminDashboard)
router.get("/system-stats", getSystemStats)

// User management routes
router.get("/users", getAllUsers)
router.put("/users/status", updateUserStatus)

// Order management routes
router.get("/orders", getAllOrdersAdmin)
router.put("/orders/status", updateOrderStatus)

// Product analytics routes
router.get("/product-analytics", getProductAnalytics)

// Legacy route for backward compatibility
router.get("/get-admin-dashboard", getAdminDashboard)

export default router