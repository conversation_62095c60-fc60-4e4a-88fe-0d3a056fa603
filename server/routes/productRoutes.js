import express from "express";
import {
  addProduct,
  getBestSellingProducts,
  getProductByProductCode,
  getProducts,
  updateProduct,
  deleteProduct,
  getAllProducts,
  searchProducts,
  getProductById,
  bulkUpdateProducts,
  bulkDeleteProducts
} from "../controllers/productController.js";
import { verifyAccessToken, verifyAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

// Public routes
router.get("/:productSlug/:productCode", getProductByProductCode);
router.get("/best-selling-products", getBestSellingProducts);
router.get("/", getProducts);                                         // /v1/products/
router.get("/search", searchProducts);                                // /v1/products/search

// Admin routes
router.post("/", verifyAccessToken, verifyAdmin, addProduct);         // /v1/products/
router.put("/:id", verifyAccessToken, verifyAdmin, updateProduct);    // /v1/products/:id
router.delete("/:productID", verifyAccessToken, verifyAdmin, deleteProduct);
router.get("/get-all", getAllProducts);                               // /v1/products/get-all (admin with pagination)
router.get("/admin/:id", verifyAccessToken, verifyAdmin, getProductById); // /v1/products/admin/:id
router.put("/bulk/update", verifyAccessToken, verifyAdmin, bulkUpdateProducts); // /v1/products/bulk/update
router.delete("/bulk/delete", verifyAccessToken, verifyAdmin, bulkDeleteProducts); // /v1/products/bulk/delete

export default router;
