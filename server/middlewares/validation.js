import { body, param, query, validationResult } from 'express-validator';
import mongoose from 'mongoose';

// Validation result handler
export const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }
    next();
};

// Common validation rules
export const validateObjectId = (field) => {
    return param(field).custom((value) => {
        if (!mongoose.Types.ObjectId.isValid(value)) {
            throw new Error(`Invalid ${field} format`);
        }
        return true;
    });
};

export const validateEmail = () => {
    return body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address');
};

export const validatePassword = () => {
    return body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number');
};

export const validatePhoneNumber = () => {
    return body('phoneNumber')
        .isMobilePhone()
        .withMessage('Please provide a valid phone number');
};

// Product validation rules
export const validateProduct = () => {
    return [
        body('storeName')
            .trim()
            .isLength({ min: 1, max: 100 })
            .withMessage('Store name must be between 1 and 100 characters'),
        
        body('title')
            .trim()
            .isLength({ min: 1, max: 200 })
            .withMessage('Product title must be between 1 and 200 characters'),
        
        body('description')
            .trim()
            .isLength({ min: 10, max: 2000 })
            .withMessage('Description must be between 10 and 2000 characters'),
        
        body('price')
            .isFloat({ min: 0.01 })
            .withMessage('Price must be a positive number'),
        
        body('stock')
            .isInt({ min: 0 })
            .withMessage('Stock must be a non-negative integer'),
        
        body('discount')
            .optional()
            .isFloat({ min: 0, max: 100 })
            .withMessage('Discount must be between 0 and 100'),
        
        body('category')
            .custom((value) => {
                if (!mongoose.Types.ObjectId.isValid(value)) {
                    throw new Error('Invalid category ID');
                }
                return true;
            }),
        
        body('mainImage')
            .isURL()
            .withMessage('Main image must be a valid URL'),
        
        body('image1')
            .isURL()
            .withMessage('Image 1 must be a valid URL'),
        
        body('image2')
            .isURL()
            .withMessage('Image 2 must be a valid URL'),
        
        body('image3')
            .isURL()
            .withMessage('Image 3 must be a valid URL'),
        
        body('image4')
            .isURL()
            .withMessage('Image 4 must be a valid URL'),
        
        body('colors')
            .optional()
            .isArray()
            .withMessage('Colors must be an array'),
        
        body('sizes')
            .optional()
            .isArray()
            .withMessage('Sizes must be an array'),
        
        body('onSale')
            .optional()
            .isBoolean()
            .withMessage('onSale must be a boolean value')
    ];
};

// User registration validation
export const validateUserRegistration = () => {
    return [
        body('fullName')
            .trim()
            .isLength({ min: 2, max: 50 })
            .withMessage('Full name must be between 2 and 50 characters')
            .matches(/^[a-zA-Z\s]+$/)
            .withMessage('Full name can only contain letters and spaces'),
        
        validateEmail(),
        validatePassword(),
        validatePhoneNumber(),
        
        body('gender')
            .optional()
            .isIn(['male', 'female', 'other'])
            .withMessage('Gender must be male, female, or other')
    ];
};

// User login validation
export const validateUserLogin = () => {
    return [
        validateEmail(),
        body('password')
            .notEmpty()
            .withMessage('Password is required')
    ];
};

// Pagination validation
export const validatePagination = () => {
    return [
        query('page')
            .optional()
            .isInt({ min: 1 })
            .withMessage('Page must be a positive integer'),
        
        query('limit')
            .optional()
            .isInt({ min: 1, max: 100 })
            .withMessage('Limit must be between 1 and 100'),
        
        query('sortBy')
            .optional()
            .isIn(['createdAt', 'updatedAt', 'title', 'price', 'stock'])
            .withMessage('Invalid sort field'),
        
        query('sortOrder')
            .optional()
            .isIn(['asc', 'desc'])
            .withMessage('Sort order must be asc or desc')
    ];
};

// Search validation
export const validateSearch = () => {
    return [
        query('search')
            .optional()
            .trim()
            .isLength({ max: 100 })
            .withMessage('Search query must not exceed 100 characters'),
        
        query('category')
            .optional()
            .custom((value) => {
                if (value && !mongoose.Types.ObjectId.isValid(value)) {
                    throw new Error('Invalid category ID');
                }
                return true;
            }),
        
        query('minPrice')
            .optional()
            .isFloat({ min: 0 })
            .withMessage('Minimum price must be non-negative'),
        
        query('maxPrice')
            .optional()
            .isFloat({ min: 0 })
            .withMessage('Maximum price must be non-negative')
    ];
};

// Order validation
export const validateOrder = () => {
    return [
        body('items')
            .isArray({ min: 1 })
            .withMessage('Order must contain at least one item'),
        
        body('items.*.product')
            .custom((value) => {
                if (!mongoose.Types.ObjectId.isValid(value)) {
                    throw new Error('Invalid product ID');
                }
                return true;
            }),
        
        body('items.*.quantity')
            .isInt({ min: 1 })
            .withMessage('Quantity must be a positive integer'),
        
        body('shippingAddress.name')
            .trim()
            .isLength({ min: 2, max: 50 })
            .withMessage('Name must be between 2 and 50 characters'),
        
        body('shippingAddress.address')
            .trim()
            .isLength({ min: 5, max: 200 })
            .withMessage('Address must be between 5 and 200 characters'),
        
        body('shippingAddress.city')
            .trim()
            .isLength({ min: 2, max: 50 })
            .withMessage('City must be between 2 and 50 characters'),
        
        body('shippingAddress.postalCode')
            .trim()
            .isLength({ min: 3, max: 10 })
            .withMessage('Postal code must be between 3 and 10 characters'),
        
        body('paymentMethod')
            .isIn(['card', 'paypal', 'cash_on_delivery'])
            .withMessage('Invalid payment method')
    ];
};

// Bulk operations validation
export const validateBulkOperation = () => {
    return [
        body('productIds')
            .isArray({ min: 1 })
            .withMessage('Product IDs array is required'),
        
        body('productIds.*')
            .custom((value) => {
                if (!mongoose.Types.ObjectId.isValid(value)) {
                    throw new Error('Invalid product ID');
                }
                return true;
            })
    ];
};
