{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@formspree/react": "^3.0.0", "@heroicons/react": "^2.2.0", "@iconify/icons-mdi": "^1.2.48", "@iconify/react": "^5.2.1", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.5.0", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "axios": "^1.7.9", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.12.6", "lucide-react": "^0.513.0", "react": "^18.3.1", "react-color": "^2.19.3", "react-dom": "^18.3.1", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.11", "react-redux": "^9.2.0", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^7.1.1", "react-toastify": "^11.0.3", "recharts": "^3.2.0", "swiper": "^11.2.0", "ui": "file:"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}