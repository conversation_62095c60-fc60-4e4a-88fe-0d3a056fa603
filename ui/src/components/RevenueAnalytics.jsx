import React, { useEffect, useState } from "react";
import { Line<PERSON>hart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid } from "recharts";
import api from "../api/axiosInstance";

const RevenueAnalytics = ({ revenueData }) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    if (revenueData && revenueData.length > 0) {
      // Format the data for the chart
      const formattedData = revenueData.map(item => ({
        date: new Date(item._id).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        revenue: item.revenue,
        orders: item.orders
      }));
      setData(formattedData);
    } else {
      // Fallback data if no real data is available
      setData([
        { date: "12 Aug", revenue: 8000, orders: 40 },
        { date: "13 Aug", revenue: 10000, orders: 50 },
        { date: "14 Aug", revenue: 12000, orders: 60 },
        { date: "15 Aug", revenue: 14000, orders: 70 },
        { date: "16 Aug", revenue: 16000, orders: 80 },
        { date: "17 Aug", revenue: 14521, orders: 75 },
        { date: "18 Aug", revenue: 13000, orders: 70 },
        { date: "19 Aug", revenue: 12500, orders: 65 },
      ]);
    }
  }, [revenueData]);

  return (
    <div className="bg-white p-4 rounded-lg shadow-md w-[585px]">
      <h2 className="text-lg font-semibold">Revenue Analytics</h2>
      <ResponsiveContainer width="100%" height={250}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Line type="monotone" dataKey="revenue" stroke="#ff7300" strokeWidth={2} />
          <Line type="monotone" dataKey="orders" stroke="#ffb800" strokeWidth={2} strokeDasharray="5 5" />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RevenueAnalytics;
