import React from 'react';
import { Link } from 'react-router-dom';
import { RotateCcw, Clock, Package, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';

const ReturnPolicy = () => {
  const returnSteps = [
    {
      icon: <Package className="h-8 w-8 text-blue-600" />,
      title: "Initiate Return",
      description: "Log into your account and select the item you want to return"
    },
    {
      icon: <RotateCcw className="h-8 w-8 text-green-600" />,
      title: "Print Return Label",
      description: "Download and print the prepaid return shipping label"
    },
    {
      icon: <Package className="h-8 w-8 text-purple-600" />,
      title: "Package Item",
      description: "Securely package the item in its original packaging"
    },
    {
      icon: <CreditCard className="h-8 w-8 text-orange-600" />,
      title: "Get Refund",
      description: "Receive your refund within 5-10 business days"
    }
  ];

  const returnableItems = [
    "Clothing and accessories in original condition",
    "Electronics with all original accessories",
    "Home and garden items (unused)",
    "Books and media in original condition",
    "Beauty products (unopened and unused)",
    "Sports and outdoor equipment"
  ];

  const nonReturnableItems = [
    "Personalized or customized items",
    "Perishable goods and food items",
    "Intimate apparel and swimwear",
    "Digital downloads and software",
    "Gift cards and vouchers",
    "Items damaged by misuse"
  ];

  return (
    <div className="min-h-screen bg-gray-50 md:mt-28 mt-40 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Breadcrumbs */}
        <div className="mb-8">
          <Link to="/" className="text-gray-500 hover:text-gray-700">Home</Link>
          <span className="mx-2 text-gray-500">/</span>
          <span className="text-gray-900">Return Policy</span>
        </div>

        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Return Policy</h1>
          <p className="text-gray-600 text-lg">
            Easy returns within 30 days - We want you to be completely satisfied with your purchase
          </p>
        </div>

        {/* Key Points */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <Clock className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">30-Day Window</h3>
            <p className="text-gray-600">Return items within 30 days of delivery for a full refund</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <RotateCcw className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Free Returns</h3>
            <p className="text-gray-600">Free return shipping for defective items or our mistakes</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <CreditCard className="h-12 w-12 text-purple-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Quick Refunds</h3>
            <p className="text-gray-600">Refunds processed within 5-10 business days</p>
          </div>
        </div>

        {/* Return Process */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">How to Return an Item</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {returnSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  {step.icon}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Return Conditions */}
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <CheckCircle className="h-6 w-6 text-green-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Returnable Items</h3>
            </div>
            <ul className="space-y-3">
              {returnableItems.map((item, index) => (
                <li key={index} className="flex items-start text-gray-700">
                  <span className="text-green-600 mr-2">✓</span>
                  {item}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <AlertCircle className="h-6 w-6 text-red-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Non-Returnable Items</h3>
            </div>
            <ul className="space-y-3">
              {nonReturnableItems.map((item, index) => (
                <li key={index} className="flex items-start text-gray-700">
                  <span className="text-red-600 mr-2">✗</span>
                  {item}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Return Conditions Details */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Return Conditions</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Item Condition Requirements</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Items must be unused and in original condition
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Original packaging and tags must be included
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  All accessories and manuals must be included
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Items must be free from damage or wear
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Return Shipping</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Free return shipping for defective items
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Customer pays return shipping for other returns
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Use provided return label for tracking
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Package items securely to prevent damage
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Refund Information */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Refund Information</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">Processing Time</h3>
              <p className="text-gray-600">5-10 business days after we receive your return</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">Refund Method</h3>
              <p className="text-gray-600">Refunded to your original payment method</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">Refund Amount</h3>
              <p className="text-gray-600">Full purchase price minus return shipping (if applicable)</p>
            </div>
          </div>
        </div>

        {/* Exchanges */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Exchanges</h2>
          <div className="space-y-4 text-gray-700">
            <p>
              We currently don't offer direct exchanges. To exchange an item for a different size, 
              color, or model, please return the original item and place a new order for the desired item.
            </p>
            <p>
              This ensures you get the item you want as quickly as possible and helps us process 
              your return more efficiently.
            </p>
          </div>
        </div>

        {/* Special Circumstances */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Special Circumstances</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Damaged or Defective Items</h3>
              <p className="text-gray-700 mb-3">
                If you receive a damaged or defective item, please contact us immediately. 
                We'll provide a prepaid return label and expedite your replacement or refund.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Wrong Item Received</h3>
              <p className="text-gray-700 mb-3">
                If we sent you the wrong item, we'll cover all return shipping costs and 
                send you the correct item at no additional charge.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="bg-blue-50 rounded-lg p-8 text-center">
          <h3 className="text-2xl font-semibold text-gray-900 mb-4">Need Help with a Return?</h3>
          <p className="text-gray-600 mb-6">
            Our customer service team is here to help make your return process as smooth as possible.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/contact" 
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Contact Support
            </Link>
            <a 
              href="mailto:<EMAIL>" 
              className="inline-block bg-white text-blue-600 border border-blue-600 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors"
            >
              Email Returns Team
            </a>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <Link 
            to="/" 
            className="inline-block bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ReturnPolicy;
