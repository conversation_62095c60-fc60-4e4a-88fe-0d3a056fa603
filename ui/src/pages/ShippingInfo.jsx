import React from 'react';
import { Link } from 'react-router-dom';
import { Truck, Clock, MapPin, Package, Shield, CreditCard } from 'lucide-react';

const ShippingInfo = () => {
  const shippingOptions = [
    {
      icon: <Truck className="h-8 w-8 text-blue-600" />,
      title: "Standard Shipping",
      time: "3-7 Business Days",
      cost: "Free on orders $140+, otherwise $10",
      description: "Reliable delivery for most locations within Bangladesh"
    },
    {
      icon: <Clock className="h-8 w-8 text-green-600" />,
      title: "Express Shipping",
      time: "1-2 Business Days",
      cost: "$20",
      description: "Fast delivery for urgent orders within major cities"
    },
    {
      icon: <MapPin className="h-8 w-8 text-purple-600" />,
      title: "International Shipping",
      time: "7-14 Business Days",
      cost: "Varies by destination",
      description: "Worldwide delivery with tracking and insurance"
    }
  ];

  const shippingZones = [
    {
      zone: "Dhaka Metropolitan Area",
      time: "1-2 Business Days",
      cost: "Free on orders $140+"
    },
    {
      zone: "Major Cities (Chittagong, Sylhet, Rajshahi)",
      time: "2-3 Business Days", 
      cost: "Free on orders $140+"
    },
    {
      zone: "Other Areas in Bangladesh",
      time: "3-7 Business Days",
      cost: "Free on orders $140+"
    },
    {
      zone: "International",
      time: "7-14 Business Days",
      cost: "Calculated at checkout"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 md:mt-28 mt-40 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Breadcrumbs */}
        <div className="mb-8">
          <Link to="/" className="text-gray-500 hover:text-gray-700">Home</Link>
          <span className="mx-2 text-gray-500">/</span>
          <span className="text-gray-900">Shipping Information</span>
        </div>

        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Shipping Information</h1>
          <p className="text-gray-600 text-lg">
            Fast, reliable delivery options to get your orders to you quickly and safely
          </p>
        </div>

        {/* Shipping Options */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {shippingOptions.map((option, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center">
              <div className="flex justify-center mb-4">
                {option.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{option.title}</h3>
              <p className="text-lg font-medium text-blue-600 mb-2">{option.time}</p>
              <p className="text-gray-600 font-medium mb-3">{option.cost}</p>
              <p className="text-gray-500 text-sm">{option.description}</p>
            </div>
          ))}
        </div>

        {/* Shipping Zones */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Shipping Zones & Delivery Times</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">Delivery Zone</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">Delivery Time</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">Shipping Cost</th>
                </tr>
              </thead>
              <tbody>
                {shippingZones.map((zone, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-3 px-4 text-gray-900">{zone.zone}</td>
                    <td className="py-3 px-4 text-gray-600">{zone.time}</td>
                    <td className="py-3 px-4 text-gray-600">{zone.cost}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Shipping Process */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">How Shipping Works</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <CreditCard className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">1. Order Placed</h3>
              <p className="text-gray-600 text-sm">Complete your purchase and payment</p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Package className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">2. Order Processed</h3>
              <p className="text-gray-600 text-sm">We prepare and package your items</p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Truck className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">3. Order Shipped</h3>
              <p className="text-gray-600 text-sm">Your package is on its way</p>
            </div>
            <div className="text-center">
              <div className="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">4. Delivered</h3>
              <p className="text-gray-600 text-sm">Package arrives safely at your door</p>
            </div>
          </div>
        </div>

        {/* Shipping Policies */}
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Shipping Policies</h3>
            <ul className="space-y-3 text-gray-700">
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Orders placed before 2 PM are processed the same day
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Free shipping on orders over $140 within Bangladesh
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Tracking information provided for all shipments
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Signature required for orders over $500
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Insurance included on all international shipments
              </li>
            </ul>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Important Notes</h3>
            <ul className="space-y-3 text-gray-700">
              <li className="flex items-start">
                <span className="text-orange-600 mr-2">•</span>
                Delivery times are estimates, not guarantees
              </li>
              <li className="flex items-start">
                <span className="text-orange-600 mr-2">•</span>
                Weather and holidays may affect delivery times
              </li>
              <li className="flex items-start">
                <span className="text-orange-600 mr-2">•</span>
                Additional customs fees may apply for international orders
              </li>
              <li className="flex items-start">
                <span className="text-orange-600 mr-2">•</span>
                PO Box addresses not accepted for certain items
              </li>
              <li className="flex items-start">
                <span className="text-orange-600 mr-2">•</span>
                Contact us if your package doesn't arrive on time
              </li>
            </ul>
          </div>
        </div>

        {/* Contact Section */}
        <div className="bg-blue-50 rounded-lg p-8 text-center">
          <h3 className="text-2xl font-semibold text-gray-900 mb-4">Questions About Shipping?</h3>
          <p className="text-gray-600 mb-6">
            Our customer service team is here to help with any shipping questions or concerns.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/contact" 
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Contact Support
            </Link>
            <a 
              href="tel:+88015-88888-9999" 
              className="inline-block bg-white text-blue-600 border border-blue-600 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors"
            >
              Call Us: +88015-88888-9999
            </a>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <Link 
            to="/" 
            className="inline-block bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ShippingInfo;
