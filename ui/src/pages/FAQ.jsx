import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown, ChevronUp } from 'lucide-react';

const FAQ = () => {
  const [openItems, setOpenItems] = useState({});

  const toggleItem = (index) => {
    setOpenItems(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const faqData = [
    {
      category: "Orders & Shipping",
      questions: [
        {
          question: "How long does shipping take?",
          answer: "Standard shipping typically takes 3-7 business days within Bangladesh. Express shipping is available for 1-2 business days delivery. International shipping may take 7-14 business days depending on the destination."
        },
        {
          question: "What are the shipping costs?",
          answer: "Shipping is free for orders over $140. For orders under $140, standard shipping costs $10 within Bangladesh. Express shipping costs $20. International shipping rates vary by destination and weight."
        },
        {
          question: "Can I track my order?",
          answer: "Yes! Once your order ships, you'll receive a tracking number via email. You can also track your order by logging into your account and visiting the 'My Orders' section."
        },
        {
          question: "Can I change or cancel my order?",
          answer: "You can modify or cancel your order within 1 hour of placing it. After that, please contact our customer service team as soon as possible. Once an order has shipped, it cannot be cancelled."
        }
      ]
    },
    {
      category: "Returns & Refunds",
      questions: [
        {
          question: "What is your return policy?",
          answer: "We offer a 30-day return policy for most items. Items must be unused, in original packaging, and in the same condition as received. Some items like personalized products or perishables cannot be returned."
        },
        {
          question: "How do I return an item?",
          answer: "To return an item, log into your account, go to 'My Orders', find the order, and click 'Return Item'. Follow the instructions to print a return label. Package the item securely and ship it back to us."
        },
        {
          question: "When will I receive my refund?",
          answer: "Refunds are processed within 5-10 business days after we receive your returned item. The refund will be credited to your original payment method. Bank processing times may vary."
        },
        {
          question: "Who pays for return shipping?",
          answer: "Return shipping costs depend on the reason for return. If the item is defective or we sent the wrong item, we'll cover return shipping. For other returns, customers are responsible for return shipping costs."
        }
      ]
    },
    {
      category: "Account & Payment",
      questions: [
        {
          question: "How do I create an account?",
          answer: "Click 'Sign Up' at the top of any page, enter your email and create a password. You can also sign up using your Google account for faster registration."
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. All payments are processed securely through encrypted connections."
        },
        {
          question: "Is my payment information secure?",
          answer: "Yes, we use industry-standard SSL encryption to protect your payment information. We never store your complete credit card details on our servers. All transactions are processed through secure payment gateways."
        },
        {
          question: "Can I save multiple addresses?",
          answer: "Yes, you can save multiple shipping and billing addresses in your account. This makes checkout faster for future orders. You can set a default address or choose different addresses for each order."
        }
      ]
    },
    {
      category: "Products & Inventory",
      questions: [
        {
          question: "How do I know if an item is in stock?",
          answer: "Product availability is shown on each product page. If an item is out of stock, you can sign up for restock notifications. We update inventory in real-time, but popular items may sell out quickly."
        },
        {
          question: "Do you offer product warranties?",
          answer: "Yes, most products come with manufacturer warranties. Warranty terms vary by product and brand. Check the product description for specific warranty information. We also offer extended warranty options for electronics."
        },
        {
          question: "Can I get product recommendations?",
          answer: "Our website shows personalized recommendations based on your browsing and purchase history. You can also contact our customer service team for personalized product advice."
        },
        {
          question: "Do you offer bulk discounts?",
          answer: "Yes, we offer bulk discounts for large orders. Contact our sales <NAME_EMAIL> with your requirements for a custom quote. Discounts typically start at orders of 50+ units."
        }
      ]
    },
    {
      category: "Customer Service",
      questions: [
        {
          question: "How can I contact customer service?",
          answer: "You can reach us via <NAME_EMAIL>, phone at +88015-88888-9999, or through our live chat feature. Our customer service team is available 24/7 to assist you."
        },
        {
          question: "Do you have a mobile app?",
          answer: "Yes! Download our mobile app from the App Store or Google Play Store. The app offers exclusive deals, push notifications for sales, and a streamlined shopping experience."
        },
        {
          question: "Can I subscribe to newsletters?",
          answer: "Yes, subscribe to our newsletter to receive exclusive offers, new product announcements, and style tips. You can subscribe at the bottom of any page or during checkout."
        },
        {
          question: "Do you offer gift cards?",
          answer: "Yes, we offer digital gift cards in various denominations. Gift cards never expire and can be used for any products on our website. They make perfect gifts for any occasion!"
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 md:mt-28 mt-40 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Breadcrumbs */}
        <div className="mb-8">
          <Link to="/" className="text-gray-500 hover:text-gray-700">Home</Link>
          <span className="mx-2 text-gray-500">/</span>
          <span className="text-gray-900">FAQ</span>
        </div>

        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h1>
          <p className="text-gray-600 text-lg">
            Find answers to common questions about shopping with Exclusive
          </p>
        </div>

        {/* FAQ Content */}
        <div className="space-y-8">
          {faqData.map((category, categoryIndex) => (
            <div key={categoryIndex} className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6 border-b border-gray-200 pb-3">
                {category.category}
              </h2>
              <div className="space-y-4">
                {category.questions.map((item, questionIndex) => {
                  const itemKey = `${categoryIndex}-${questionIndex}`;
                  const isOpen = openItems[itemKey];
                  
                  return (
                    <div key={questionIndex} className="border border-gray-200 rounded-lg">
                      <button
                        onClick={() => toggleItem(itemKey)}
                        className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors"
                      >
                        <span className="font-medium text-gray-900">{item.question}</span>
                        {isOpen ? (
                          <ChevronUp className="h-5 w-5 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-500" />
                        )}
                      </button>
                      {isOpen && (
                        <div className="px-6 pb-4">
                          <p className="text-gray-700 leading-relaxed">{item.answer}</p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Contact Section */}
        <div className="bg-blue-50 rounded-lg p-8 mt-8 text-center">
          <h3 className="text-2xl font-semibold text-gray-900 mb-4">Still have questions?</h3>
          <p className="text-gray-600 mb-6">
            Can't find the answer you're looking for? Our customer service team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/contact" 
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Contact Us
            </Link>
            <a 
              href="mailto:<EMAIL>" 
              className="inline-block bg-white text-blue-600 border border-blue-600 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors"
            >
              Email Support
            </a>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <Link 
            to="/" 
            className="inline-block bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
